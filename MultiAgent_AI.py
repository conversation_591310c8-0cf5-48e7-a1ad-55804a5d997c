#!/usr/bin/env python3
"""
Generic CrewAI Agent Factory - LM Studio Compatible
Dynamically creates specialized agents from markdown specification files.
Agents execute based on the prompt/instructions described in the markdown spec.
"""

import re
import os
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime

import yaml

# https://github.com/psf/requests
import requests

# https://docs.crewai.com/en/
from crewai import Agent, Task, Crew, Process
from crewai.llm import LLM


@dataclass
class AgentSpecification:
    """Parsed agent specification from markdown"""

    name: str
    description: str
    model: str
    full_content: str
    focus_areas: List[str]
    approach: List[str]
    rules: str
    output_format: str


class LMStudioConfig:
    """Configuration pour LM Studio"""

    BASE_URL = "http://localhost:1234/v1"
    DEFAULT_MODEL = "lm-studio"  # Modèle par défaut

    @classmethod
    def set_base_url(cls, url: str):
        """Définit l'URL de base pour LM Studio."""
        if url:
            cls.BASE_URL = url

    @classmethod
    def get_available_models(cls) -> List[str]:
        """Récupère la liste des modèles disponibles depuis LM Studio"""
        try:
            response = requests.get(f"{cls.BASE_URL}/models", timeout=5)
            if response.status_code == 200:
                models_data = response.json()
                return [model["id"] for model in models_data.get("data", [])]
        except requests.RequestException as e:
            print(f"Impossible de récupérer les modèles LM Studio: {e}")

        return []

    @classmethod
    def create_llm(
        cls, model_name: str = "", temperature: float = 0.7, max_tokens: int = 4000
    ) -> LLM:
        """Crée une instance LLM configurée pour LM Studio"""
        if not model_name:
            model_name = f"lm_studio/{cls.DEFAULT_MODEL}"

        return LLM(
            model=model_name,  # "lm_studio/" + cls.DEFAULT_MODEL,
            base_url=cls.BASE_URL,
            temperature=temperature,
            max_tokens=max_tokens,
            api_key="lm-studio",  # LM Studio utilise une clé factice
        )


class MarkdownSpecParser:
    """Parser for markdown agent specifications"""

    @staticmethod
    def parse_frontmatter(content: str) -> Dict[str, Any]:
        """Extract YAML frontmatter from markdown"""
        if content.startswith("---"):
            parts = content.split("---", 2)
            if len(parts) >= 3:
                try:
                    return yaml.safe_load(parts[1])
                except yaml.YAMLError:
                    return {}
        return {}

    @staticmethod
    def extract_markdown_body(content: str) -> str:
        """Extract markdown body (everything after frontmatter)"""
        if content.startswith("---"):
            parts = content.split("---", 2)
            if len(parts) >= 3:
                return parts[2].strip()
        return content.strip()

    @staticmethod
    def extract_sections(content: str) -> Dict[str, str]:
        """Extract sections from markdown content"""
        sections = {}
        current_section = None
        current_content = []

        lines = content.split("\n")
        for line in lines:
            if line.startswith("## "):
                if current_section:
                    sections[current_section] = "\n".join(current_content).strip()
                current_section = line[3:].strip()
                current_content = []
            elif current_section:
                current_content.append(line)

        if current_section:
            sections[current_section] = "\n".join(current_content).strip()

        return sections

    @staticmethod
    def parse_list_items(text: str) -> List[str]:
        """Extract list items from text"""
        items = []
        for line in text.split("\n"):
            line = line.strip()
            if line.startswith("- ") or line.startswith("* "):
                items.append(line[2:].strip())
            elif re.match(r"^\d+\.", line):
                items.append(re.sub(r"^\d+\.\s*", "", line))
        return [item for item in items if item]  # Filter empty items

    @classmethod
    def parse_specification(cls, file_path: str) -> AgentSpecification:
        """Parse a complete agent specification from markdown file"""
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        frontmatter = cls.parse_frontmatter(content)
        markdown_body = cls.extract_markdown_body(content)
        sections = cls.extract_sections(markdown_body)

        # Utilise le modèle par défaut si non spécifié
        model = frontmatter.get("model", LMStudioConfig.DEFAULT_MODEL)

        return AgentSpecification(
            name=frontmatter.get("name", Path(file_path).stem),
            description=frontmatter.get("description", ""),
            model=model,
            full_content=markdown_body,  # Use full markdown as system prompt
            focus_areas=cls.parse_list_items(sections.get("Focus Areas", "")),
            approach=cls.parse_list_items(sections.get("Approach", "")),
            rules=sections.get(
                "Optimization Rules",
                sections.get("Keyword Density Guidelines", sections.get("Rules", "")),
            ),
            output_format=sections.get("Output", ""),
        )


class GenericAgentFactory:
    """Factory for creating agents from markdown specifications"""

    @classmethod
    def create_agent_from_spec(cls, spec_file: str) -> tuple[Agent, AgentSpecification]:
        """Create a CrewAI agent from markdown specification"""
        spec = MarkdownSpecParser.parse_specification(spec_file)

        # Créer l'instance LLM pour LM Studio
        llm = LMStudioConfig.create_llm(spec.model)

        # Create the agent using the markdown content as system instructions
        agent = Agent(
            role=cls._create_role_from_spec(spec),
            goal=spec.description,
            backstory=cls._create_backstory_from_spec(spec),
            tools=[],
            verbose=True,
            allow_delegation=False,
            llm=llm,
        )

        return agent, spec

    @classmethod
    def _create_role_from_spec(cls, spec: AgentSpecification) -> str:
        """Generate role description from specification"""
        # Extract role from the first line of the markdown or use name
        lines = spec.full_content.split("\n")
        for line in lines:
            if "You are" in line:
                return (
                    line.replace("You are a ", "")
                    .replace("You are an ", "")
                    .replace("You are ", "")
                    .strip(".")
                )

        # Fallback to generating from name
        return spec.name.replace("-", " ").title() + " Specialist"

    @classmethod
    def _create_backstory_from_spec(cls, spec: AgentSpecification) -> str:
        """Generate backstory from specification"""
        focus_areas_text = (
            ", ".join(spec.focus_areas[:3]) if spec.focus_areas else "specialized tasks"
        )

        return f"""You are an expert in {focus_areas_text}. You follow the exact guidelines
        and instructions provided in your specification. Your expertise includes understanding
        and implementing all the rules, approaches, and output formats specified in your
        configuration. You always deliver results that precisely match the specified requirements."""


class AgentManager:
    """Manages multiple specialized agents loaded from markdown specifications"""

    def __init__(self, specs_directory: str = "agent_specs", lm_studio_url: str = ""):
        self.specs_directory = Path(specs_directory)
        LMStudioConfig.set_base_url(lm_studio_url)
        self.agents = {}
        self.specifications = {}
        self._check_lm_studio_connection()
        self._load_agents()

    def _check_lm_studio_connection(self):
        """Vérifie la connexion à LM Studio"""
        try:
            response = requests.get(f"{LMStudioConfig.BASE_URL}/models", timeout=5)
            if response.status_code == 200:
                print(f"✅ Connexion LM Studio établie: {LMStudioConfig.BASE_URL}")
                models = LMStudioConfig.get_available_models()
                print(f"📋 Modèles disponibles: {len(models)}")
            else:
                print(f"⚠️ LM Studio accessible mais erreur: {response.status_code}")
        except requests.RequestException as e:
            print(
                f"❌ Impossible de se connecter à LM Studio ({LMStudioConfig.BASE_URL}): {e}"
            )
            print("💡 Assurez-vous que LM Studio est lancé et accessible")

    def _load_agents(self):
        """Load all agents from specification files"""
        if not self.specs_directory.exists():
            print(f"📁 Création du répertoire specs: {self.specs_directory}")
            self.specs_directory.mkdir(parents=True, exist_ok=True)
            return

        loaded_count = 0
        for spec_file in self.specs_directory.glob("*.md"):
            try:
                agent, spec = GenericAgentFactory.create_agent_from_spec(str(spec_file))

                self.agents[spec.name] = agent
                self.specifications[spec.name] = spec
                loaded_count += 1
                print(f"✅ Agent chargé: {spec.name} (modèle: {spec.model})")
            except (OSError, yaml.YAMLError) as e:
                print(f"❌ Échec du chargement de {spec_file}: {e}")

        print(f"📊 Total agents chargés: {loaded_count}")

    def get_agent(self, name: str) -> Optional[Agent]:
        """Get agent by name"""
        return self.agents.get(name)

    def get_specification(self, name: str) -> Optional[AgentSpecification]:
        """Get agent specification by name"""
        return self.specifications.get(name)

    def list_agents(self) -> List[str]:
        """List available agent names"""
        return list(self.agents.keys())

    def get_agent_info(self, name: str) -> Dict[str, Any]:
        """Obtient des informations détaillées sur un agent"""
        spec = self.get_specification(name)
        if not spec:
            return {}

        return {
            "name": spec.name,
            "description": spec.description,
            "model": spec.model,
            "focus_areas": spec.focus_areas,
            "approach": spec.approach,
            "has_rules": bool(spec.rules),
            "has_output_format": bool(spec.output_format),
        }

    def invoke_agent(self, agent_name: str, user_input: str, **kwargs) -> str:
        """Invoke a specific agent with user input"""
        agent = self.get_agent(agent_name)
        spec = self.get_specification(agent_name)

        if not agent or not spec:
            return f"❌ Agent '{agent_name}' non trouvé"

        print(f"🤖 Invocation de l'agent: {agent_name}")
        print(f"📝 Modèle utilisé: {spec.model}")

        # Create a comprehensive task description that includes the full spec
        task_description = f"""
{spec.full_content}

---

User Request: {user_input}

Please follow the guidelines, rules, and output format specified above to complete this request.
"""

        task = Task(
            description=task_description,
            expected_output=spec.output_format
            or "Detailed response following the specification guidelines",
            agent=agent,
        )

        try:
            crew = Crew(
                agents=[agent],
                tasks=[task],
                process=Process.sequential,
                verbose=kwargs.get("verbose", True),
            )

            print("🚀 Exécution en cours...")
            result = crew.kickoff()
            print("✅ Tâche terminée")
            return str(result)
        except RuntimeError as e:
            error_msg = (
                f"❌ Erreur lors de l'exécution de l'agent {agent_name}: {str(e)}"
            )
            print(error_msg)
            return error_msg

    def reload_agents(self):
        """Reload all agents from specification files"""
        print("🔄 Rechargement des agents...")
        self.agents.clear()
        self.specifications.clear()
        self._check_lm_studio_connection()
        self._load_agents()


class SubAgentInterface:
    """Simple interface for subagent pattern like Claude Code"""

    def __init__(self, specs_directory: str = "agent_specs", lm_studio_url: str = ""):
        self.manager = AgentManager(specs_directory, lm_studio_url)

    def invoke(self, agent_name: str, prompt: str, **kwargs) -> str:
        """Simple invoke interface"""
        return self.manager.invoke_agent(agent_name, prompt, **kwargs)

    def list_available(self) -> List[str]:
        """List available sub-agents"""
        return self.manager.list_agents()

    def get_info(self, agent_name: str = "") -> Dict[str, Any]:
        """Obtient des informations sur un agent spécifique ou tous les agents"""
        if agent_name:
            return self.manager.get_agent_info(agent_name)
        else:
            return {
                name: self.manager.get_agent_info(name)
                for name in self.list_available()
            }

    def check_health(self) -> Dict[str, Any]:
        """Vérifie l'état du système"""
        return {
            "lm_studio_url": LMStudioConfig.BASE_URL,
            "available_models": LMStudioConfig.get_available_models(),
            "loaded_agents": len(self.list_available()),
            "agent_names": self.list_available(),
        }


def main():
    """Fonction principale avec exemples d'utilisation"""

    lm_studio_url = os.getenv("LM_STUDIO_URL", "http://localhost:1234/v1")

    print("🚀 Initialisation du système AI MultiAgent avec LM Studio")
    print("=" * 57)

    sub_agents = SubAgentInterface("agent_specs", lm_studio_url)

    available_agents = sub_agents.list_available()
    if not available_agents:
        print(
            "❌ Aucun agent disponible. Créez des fichiers .md dans le dossier agent_specs/"
        )
        return

    health = sub_agents.check_health()
    print("🏥 État du système:")
    for key, value in health.items():
        print(f"   {key}: {value}")

    print(f"🤖 Agents disponibles: {available_agents}")

    agent_1 = available_agents[0]
    prompt_1 = "Create optimized meta tags for a blog post about 'Python FastAPI Tutorial for Beginners - Build REST APIs Fast' targeting web developers"
    agent_2 = available_agents[1]
    prompt_2 = "Create a keyword strategy for an article on 'Python MCP Tutorial for Beginners - Build MCP server Fast' targeting developers. Goal: INFORMATIONAL_TUTORIAL search intent - users seeking step-by-step learning content for MCP implementation."

    prompt_agent = prompt_2
    seo_agent = agent_2

    print(f"💡 Exemple d'utilisation avec l'agent: '{seo_agent}'")
    print("=" * 57)

    agent_info = sub_agents.get_info(seo_agent)
    print("📋 Informations de l'agent:")
    for key, value in agent_info.items():
        print(f"   {key}: {value}")

    print(f"📤 Envoi de la requête : {prompt_agent}")

    try:
        result = sub_agents.invoke(seo_agent, prompt_agent)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"agent_result_{seo_agent}_{timestamp}.md"
        with open(filename, "w", encoding="utf-8") as f:
            f.write(f"# Agent Result: {seo_agent}, Model: {agent_info['model']}\n\n")
            f.write(f"**Timestamp:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"**Prompt:** {prompt_agent}\n\n")
            f.write("## Result\n\n")
            f.write(str(result))

        print(f"✅ Résultat sauvegardé dans '{filename}'")
    except RuntimeError as e:
        print(f"❌ Erreur lors du test: {e}")


if __name__ == "__main__":
    main()
